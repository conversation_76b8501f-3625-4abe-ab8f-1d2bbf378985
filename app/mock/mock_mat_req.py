import random
from app.dto.mat_req_dto import (
    CreateMatReq,
    CreateMatReqBundleInfoWithQuantity,
    CreateMatReqWithQuantity,
)


def mock_mat_req_CheckStockAvailable_response(payload: CreateMatReq):
    data = []
    list_of_bundle_info = payload.mat_req_bundles
    for bundle_info in list_of_bundle_info:
        CreateMatReqBundleInfoWithQuantity(
            micron_part_num=bundle_info.micron_part_num,
            unit_of_measures="Die",
            quantity=random.randint(10000, 25000),  # Randomize quantity
            material_type=bundle_info.material_type,
        )
    return CreateMatReqWithQuantity(mat_req_bundles=data)
