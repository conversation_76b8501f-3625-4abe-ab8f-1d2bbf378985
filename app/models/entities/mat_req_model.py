from sqlalchemy import Enum
from typing import List, Dict, Any
from sqlmodel import SQLModel, Field, Column, JSON

from app.utils.enum import MatReqStatus
from app.utils.conversion import convert_utc_to_local_iso
from app.models.entities.base_timestamp import BaseTimestamp


class MatReqBase(SQLModel):
    id: int = Field(primary_key=True)
    mat_req_no: str = Field(nullable=True)
    status: str = Field(sa_column=Enum(MatReqStatus), default=MatReqStatus.PENDING)
    data: List[Dict[str, Any]] = Field(sa_column=Column(JSON), default=[])


class MatReq(BaseTimestamp, MatReqBase, table=True):
    __tablename__ = "material_request"

    class Config:
        from_attributes = True

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the MatReq instance to a dictionary.

        Returns:
            Dict[str, Any]: Dictionary representation of the MatReq instance
        """
        return {
            "id": self.id,
            "mat_req_no": self.mat_req_no,
            "status": self.status.value if hasattr(self.status, "value") else self.status,
            "data": self.data,
            "created_at": convert_utc_to_local_iso(self.created_at) if self.created_at else None,
            "updated_at": convert_utc_to_local_iso(self.updated_at) if self.updated_at else None,
            "completed_at": convert_utc_to_local_iso(self.completed_at) if self.completed_at else None,
        }
