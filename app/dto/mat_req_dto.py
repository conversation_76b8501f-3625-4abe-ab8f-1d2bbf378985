from typing import List
from pydantic import BaseModel, Field


class CreateMatReqBundleInfo(BaseModel):
    micron_part_num: str = Field(..., description="Part number")
    material_type: str = Field(..., description="Material type")


class CreateMatReq(BaseModel):
    # {"micronPN": "310-L300145A384E","MaterialType": "SBT"},{"micronPN": "310-L3001450000A","MaterialType": "SBT"}, ….........
    mat_req_bundles: List[CreateMatReqBundleInfo] = Field(
        ..., description="Bundles information in this material request"
    )


class CreateMatReqBundleInfoWithQuantity(BaseModel):
    # {"micronPN": "310-L300145A384E","unitOfMeasures": "Die","quantity": 25000,"MaterialType": "SBT"}
    micron_part_num: str = Field(..., description="Micron part number")
    unit_of_measures: str = Field(..., description="Unit of measures")
    quantity: int = Field(..., description="Quantity")
    material_type: str = Field(..., description="Material type")


class CreateMatReqWithQuantity(BaseModel):
    mat_req_bundles: List[CreateMatReqBundleInfoWithQuantity] = Field(
        ..., description="Bundles information with quantity in this material request"
    )
