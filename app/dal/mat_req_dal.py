from sqlalchemy.ext.asyncio import AsyncSession

from .base_dal import BaseDAL
from app.dto.mat_req_dto import CreateMatReqWithQuantity
from app.models.entities.mat_req_model import MatReq


class MatReqDAL(BaseDAL):

    def __init__(self, session: AsyncSession):
        super().__init__(session)

    async def create_mat_req(self, mat_req_info: CreateMatReqWithQuantity) -> MatReq:
        mat_req = MatReq(data=mat_req_info.mat_req_bundles)
        self.session.add(mat_req)
        await self.session.flush()
        await self.session.refresh(mat_req)
        return mat_req
