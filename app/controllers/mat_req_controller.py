from sqlalchemy.ext.asyncio import AsyncSession

from .base_controller import BaseController
from app.dal.mat_req_dal import MatReqDAL
from app.dto.mat_req_dto import CreateMatReqWithQuantity
from app.models.entities.mat_req_model import MatReq


class MatReqController(BaseController):

    def __init__(self, session: AsyncSession):
        super().__init__(session)
        self.dal = MatReqDAL(session)

    async def create_mat_req(self, mat_req_info: CreateMatReqWithQuantity) -> MatReq:
        return await self.dal.create_mat_req(mat_req_info)
