from fastapi import APIRouter

from app.db.session import SessionDep
from app.dto.mat_req_dto import CreateMatReq
from app.utils.logger import recv_http_logger
from app.core.decorators import async_log_and_return_error
from app.controllers.mat_req_controller import MatReqController
from app.core.response import Response

router = APIRouter(prefix="/mat_req", tags=["mat_req"])


@router.post("/")
@async_log_and_return_error(recv_http_logger)
async def create_mat_req(args: CreateMatReq, session: SessionDep):
    """
    For FE to create a material request \n
    Will call Micron API for verification, if success only create Mat Req on our system
    """

    # TODO call micron api to verify bundle list
    # Order	CheckStockAvailable	{"micronPN": "310-L300145A384E","MaterialType": "SBT"},{"micronPN": "310-L3001450000A","MaterialType": "SBT"}, ….........
    # Assuming the api will response the die quantity for each part number
    # {"micronPN": "310-L300145A384E","unitOfMeasures": "Die","quantity": 25000,"MaterialType": "SBT"}
    # {"micronPN": "310-L3001450000A","unitOfMeasures": "Die","quantity": 25000,"MaterialType": "SBT"}
    from app.mock.mock_mat_req import mock_mat_req_CheckStockAvailable_response

    mock_response = mock_mat_req_CheckStockAvailable_response(args)

    mat_req_controller = MatReqController(session)
    mat_req = mat_req_controller.create_mat_req(mock_response)

    # We will save the response of each part number to the mat req
    return Response(data=mat_req)
